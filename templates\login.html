{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام حجز المواعيد{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-sign-in-alt text-primary mb-3" style="font-size: 3rem;"></i>
                        <h2 class="fw-bold">تسجيل الدخول</h2>
                        <p class="text-muted">أهلاً بك مرة أخرى</p>
                    </div>
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            {{ form.username(class="form-control", id="username", placeholder="أدخل اسم المستخدم") }}
                            {% if form.username.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            {{ form.password(class="form-control", type="password", id="password", placeholder="أدخل كلمة المرور") }}
                            {% if form.password.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <p class="mb-0">
                                ليس لديك حساب؟ 
                                <a href="{{ url_for('register') }}" class="text-decoration-none">
                                    <i class="fas fa-user-plus me-1"></i>أنشئ حساباً جديداً
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
