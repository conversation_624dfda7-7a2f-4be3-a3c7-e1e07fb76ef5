#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام حجز المواعيد - Appointment Booking System
Flask application for managing appointments with automatic notifications
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import StringField, DateTimeField, SelectField, TextAreaField, EmailField, TelField, PasswordField
from wtforms.validators import DataRequired, Email, Length
from datetime import datetime, timedelta
import os
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
# Email functionality will be implemented later
# import smtplib
import threading
import time

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///appointments.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات البريد الإلكتروني
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # غيّر هذا
app.config['MAIL_PASSWORD'] = 'your-app-password'     # غيّر هذا

# تهيئة قاعدة البيانات ونظام تسجيل الدخول
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    password_hash = db.Column(db.String(120), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    appointments = db.relationship('Appointment', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Service(db.Model):
    """نموذج الخدمات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    duration = db.Column(db.Integer, nullable=False)  # بالدقائق
    price = db.Column(db.Float, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    
    # العلاقات
    appointments = db.relationship('Appointment', backref='service', lazy=True)

class Appointment(db.Model):
    """نموذج المواعيد"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
    appointment_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='مؤكد')  # مؤكد، ملغي، مكتمل
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    reminder_sent = db.Column(db.Boolean, default=False)
    
    def __repr__(self):
        return f'<Appointment {self.id}: {self.user.username} - {self.service.name}>'

class Notification(db.Model):
    """نموذج الإشعارات"""
    id = db.Column(db.Integer, primary_key=True)
    appointment_id = db.Column(db.Integer, db.ForeignKey('appointment.id'), nullable=False)
    notification_type = db.Column(db.String(20), nullable=False)  # email, sms
    sent_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='sent')  # sent, failed
    
    appointment = db.relationship('Appointment', backref='notifications')

# نماذج الويب
class AppointmentForm(FlaskForm):
    """نموذج حجز موعد"""
    service_id = SelectField('الخدمة', coerce=int, validators=[DataRequired()])
    appointment_date = DateTimeField('تاريخ ووقت الموعد', validators=[DataRequired()])
    notes = TextAreaField('ملاحظات إضافية')

class UserRegistrationForm(FlaskForm):
    """نموذج تسجيل مستخدم جديد"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)])
    email = EmailField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    phone = TelField('رقم الهاتف')
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])

# وظائف الإشعارات
def send_email_notification(to_email, subject, body):
    """إرسال إشعار بالبريد الإلكتروني"""
    # Email functionality disabled for demo - would need proper SMTP setup
    print(f"📧 Email notification would be sent to: {to_email}")
    print(f"📧 Subject: {subject}")
    print(f"📧 Body: {body[:100]}...")
    return True

def send_appointment_confirmation(appointment):
    """إرسال تأكيد الموعد"""
    subject = "تأكيد حجز موعد"
    body = f"""
    مرحباً {appointment.user.username},
    
    تم تأكيد حجز موعدك بنجاح:
    
    الخدمة: {appointment.service.name}
    التاريخ والوقت: {appointment.appointment_date.strftime('%Y-%m-%d %H:%M')}
    السعر: {appointment.service.price} ريال
    
    شكراً لاختيارك خدماتنا.
    """
    
    if send_email_notification(appointment.user.email, subject, body):
        notification = Notification(
            appointment_id=appointment.id,
            notification_type='email',
            status='sent'
        )
        db.session.add(notification)
        db.session.commit()

def send_appointment_reminder(appointment):
    """إرسال تذكير بالموعد"""
    subject = "تذكير بموعدك"
    body = f"""
    مرحباً {appointment.user.username},
    
    تذكير بموعدك غداً:
    
    الخدمة: {appointment.service.name}
    التاريخ والوقت: {appointment.appointment_date.strftime('%Y-%m-%d %H:%M')}
    
    نتطلع لرؤيتك.
    """
    
    if send_email_notification(appointment.user.email, subject, body):
        appointment.reminder_sent = True
        notification = Notification(
            appointment_id=appointment.id,
            notification_type='email',
            status='sent'
        )
        db.session.add(notification)
        db.session.commit()

# مهمة التذكيرات التلقائية
def check_reminders():
    """فحص المواعيد التي تحتاج تذكير"""
    while True:
        try:
            with app.app_context():
                tomorrow = datetime.now() + timedelta(days=1)
                appointments = Appointment.query.filter(
                    Appointment.appointment_date.between(
                        tomorrow.replace(hour=0, minute=0, second=0),
                        tomorrow.replace(hour=23, minute=59, second=59)
                    ),
                    Appointment.reminder_sent == False,
                    Appointment.status == 'مؤكد'
                ).all()

                for appointment in appointments:
                    send_appointment_reminder(appointment)

            time.sleep(3600)  # فحص كل ساعة
        except Exception as e:
            print(f"خطأ في فحص التذكيرات: {e}")
            time.sleep(3600)

# المسارات (Routes)
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    services = Service.query.filter_by(is_active=True).all()
    return render_template('index.html', services=services)

@app.route('/register', methods=['GET', 'POST'])
def register():
    """تسجيل مستخدم جديد"""
    form = UserRegistrationForm()
    if form.validate_on_submit():
        # التحقق من عدم وجود المستخدم مسبقاً
        if User.query.filter_by(username=form.username.data).first():
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('register.html', form=form)

        if User.query.filter_by(email=form.email.data).first():
            flash('البريد الإلكتروني مسجل مسبقاً', 'error')
            return render_template('register.html', form=form)

        # إنشاء مستخدم جديد
        user = User(
            username=form.username.data,
            email=form.email.data,
            phone=form.phone.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()

        flash('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.', 'success')
        return redirect(url_for('login'))

    return render_template('register.html', form=form)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة تحكم المستخدم"""
    appointments = Appointment.query.filter_by(user_id=current_user.id).order_by(Appointment.appointment_date.desc()).all()
    return render_template('dashboard.html', appointments=appointments, now=datetime.now())

@app.route('/book', methods=['GET', 'POST'])
@login_required
def book_appointment():
    """حجز موعد جديد"""
    form = AppointmentForm()
    services = Service.query.filter_by(is_active=True).all()
    form.service_id.choices = [(s.id, f"{s.name} - {s.price} ريال") for s in services]

    if form.validate_on_submit():
        # التحقق من أن الموعد في المستقبل
        if form.appointment_date.data <= datetime.now():
            flash('يجب أن يكون الموعد في المستقبل', 'error')
            return render_template('book.html', form=form)

        # التحقق من عدم تضارب المواعيد
        service = Service.query.get(form.service_id.data)
        appointment_end = form.appointment_date.data + timedelta(minutes=service.duration)

        conflicting = Appointment.query.filter(
            Appointment.appointment_date < appointment_end,
            Appointment.appointment_date + timedelta(minutes=Service.query.get(Appointment.service_id).duration) > form.appointment_date.data,
            Appointment.status == 'مؤكد'
        ).first()

        if conflicting:
            flash('هذا الوقت محجوز مسبقاً، يرجى اختيار وقت آخر', 'error')
            return render_template('book.html', form=form)

        # إنشاء الموعد
        appointment = Appointment(
            user_id=current_user.id,
            service_id=form.service_id.data,
            appointment_date=form.appointment_date.data,
            notes=form.notes.data
        )
        db.session.add(appointment)
        db.session.commit()

        # إرسال تأكيد
        send_appointment_confirmation(appointment)

        flash('تم حجز الموعد بنجاح!', 'success')
        return redirect(url_for('dashboard'))

    return render_template('book.html', form=form, services=services, today=datetime.now().strftime('%Y-%m-%d'))

@app.route('/cancel/<int:appointment_id>')
@login_required
def cancel_appointment(appointment_id):
    """إلغاء موعد"""
    appointment = Appointment.query.get_or_404(appointment_id)

    # التحقق من أن المستخدم يملك هذا الموعد
    if appointment.user_id != current_user.id and not current_user.is_admin:
        flash('غير مسموح لك بإلغاء هذا الموعد', 'error')
        return redirect(url_for('dashboard'))

    appointment.status = 'ملغي'
    db.session.commit()

    flash('تم إلغاء الموعد بنجاح', 'info')
    return redirect(url_for('dashboard'))

@app.route('/admin')
@login_required
def admin_dashboard():
    """لوحة تحكم الإدارة"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    today = datetime.now().date()
    appointments_today = Appointment.query.filter(
        db.func.date(Appointment.appointment_date) == today,
        Appointment.status == 'مؤكد'
    ).all()

    total_appointments = Appointment.query.count()
    total_users = User.query.count()

    return render_template('admin_dashboard.html',
                         appointments_today=appointments_today,
                         total_appointments=total_appointments,
                         total_users=total_users)

@app.route('/admin/appointments')
@login_required
def admin_appointments():
    """إدارة جميع المواعيد"""
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    appointments = Appointment.query.order_by(Appointment.appointment_date.desc()).all()
    return render_template('admin_appointments.html', appointments=appointments)

@app.route('/api/available_slots')
def available_slots():
    """API للحصول على الأوقات المتاحة"""
    date_str = request.args.get('date')
    service_id = request.args.get('service_id')

    if not date_str or not service_id:
        return jsonify({'error': 'Missing parameters'}), 400

    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
        service = Service.query.get(service_id)

        if not service:
            return jsonify({'error': 'Service not found'}), 404

        # إنشاء قائمة بالأوقات المتاحة (من 9 صباحاً إلى 6 مساءً)
        available_slots = []
        start_time = datetime.combine(date, datetime.min.time().replace(hour=9))
        end_time = datetime.combine(date, datetime.min.time().replace(hour=18))

        current_time = start_time
        while current_time < end_time:
            # التحقق من عدم وجود موعد في هذا الوقت
            appointment_end = current_time + timedelta(minutes=service.duration)

            conflicting = Appointment.query.filter(
                Appointment.appointment_date < appointment_end,
                Appointment.appointment_date + timedelta(minutes=Service.query.get(Appointment.service_id).duration) > current_time,
                Appointment.status == 'مؤكد'
            ).first()

            if not conflicting and current_time > datetime.now():
                available_slots.append(current_time.strftime('%H:%M'))

            current_time += timedelta(minutes=30)  # فترات نصف ساعة

        return jsonify({'slots': available_slots})

    except ValueError:
        return jsonify({'error': 'Invalid date format'}), 400

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # إنشاء خدمات افتراضية إذا لم تكن موجودة
        if Service.query.count() == 0:
            services = [
                Service(name='قص شعر رجالي', description='قص شعر عادي للرجال', duration=30, price=25.0),
                Service(name='حلاقة ذقن', description='حلاقة وتهذيب الذقن', duration=20, price=15.0),
                Service(name='قص وتصفيف نسائي', description='قص وتصفيف الشعر للنساء', duration=60, price=50.0),
                Service(name='كشف طبي عام', description='فحص طبي شامل', duration=45, price=100.0),
                Service(name='استشارة طبية', description='استشارة طبية متخصصة', duration=30, price=80.0)
            ]
            for service in services:
                db.session.add(service)
            db.session.commit()

    # بدء مهمة التذكيرات في خيط منفصل (معطلة للعرض التوضيحي)
    # reminder_thread = threading.Thread(target=check_reminders, daemon=True)
    # reminder_thread.start()

    print("🚀 نظام حجز المواعيد يعمل الآن!")
    print("📅 افتح المتصفح على: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
