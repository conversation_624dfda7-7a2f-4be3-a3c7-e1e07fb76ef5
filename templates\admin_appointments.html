{% extends "base.html" %}

{% block title %}إدارة المواعيد - نظام حجز المواعيد{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="fw-bold">
                        <i class="fas fa-list text-primary me-2"></i>
                        إدارة المواعيد
                    </h1>
                    <p class="text-muted">عرض وإدارة جميع المواعيد في النظام</p>
                </div>
                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للوحة الإدارة
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">تصفية حسب الحالة</label>
                            <select class="form-select" id="statusFilter" onchange="filterAppointments()">
                                <option value="">جميع الحالات</option>
                                <option value="مؤكد">مؤكد</option>
                                <option value="ملغي">ملغي</option>
                                <option value="مكتمل">مكتمل</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="dateFrom" onchange="filterAppointments()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateTo" onchange="filterAppointments()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">بحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="اسم العميل أو الخدمة..." onkeyup="filterAppointments()">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>جميع المواعيد
                    </h5>
                    <span class="badge bg-primary" id="appointmentCount">{{ appointments|length }} موعد</span>
                </div>
                <div class="card-body">
                    {% if appointments %}
                        <div class="table-responsive">
                            <table class="table table-hover" id="appointmentsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>رقم الموعد</th>
                                        <th>العميل</th>
                                        <th>الخدمة</th>
                                        <th>التاريخ والوقت</th>
                                        <th>المدة</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الحجز</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in appointments %}
                                    <tr data-status="{{ appointment.status }}" data-date="{{ appointment.appointment_date.strftime('%Y-%m-%d') }}" data-search="{{ appointment.user.username|lower }} {{ appointment.service.name|lower }}">
                                        <td>
                                            <strong>#{{ appointment.id }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ appointment.user.username }}</strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    {{ appointment.user.email }}
                                                </small>
                                                {% if appointment.user.phone %}
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-phone me-1"></i>
                                                    {{ appointment.user.phone }}
                                                </small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-{{ 'cut' if 'قص' in appointment.service.name else 'stethoscope' if 'طبي' in appointment.service.name else 'user-md' }} text-primary me-2"></i>
                                                <div>
                                                    <strong>{{ appointment.service.name }}</strong>
                                                    {% if appointment.service.description %}
                                                    <br>
                                                    <small class="text-muted">{{ appointment.service.description }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ appointment.appointment_date.strftime('%Y-%m-%d') }}
                                                <br>
                                                <i class="fas fa-clock me-1"></i>
                                                {{ appointment.appointment_date.strftime('%H:%M') }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ appointment.service.duration }} دقيقة
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                {{ appointment.service.price }} ريال
                                            </span>
                                        </td>
                                        <td>
                                            {% if appointment.status == 'مؤكد' %}
                                                <span class="status-badge status-confirmed">
                                                    <i class="fas fa-check me-1"></i>مؤكد
                                                </span>
                                            {% elif appointment.status == 'ملغي' %}
                                                <span class="status-badge status-cancelled">
                                                    <i class="fas fa-times me-1"></i>ملغي
                                                </span>
                                            {% elif appointment.status == 'مكتمل' %}
                                                <span class="status-badge status-completed">
                                                    <i class="fas fa-check-double me-1"></i>مكتمل
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ appointment.created_at.strftime('%Y-%m-%d %H:%M') }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                {% if appointment.status == 'مؤكد' %}
                                                    <button class="btn btn-outline-success" onclick="markCompleted({{ appointment.id }})" title="تم">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" onclick="cancelAppointment({{ appointment.id }})" title="إلغاء">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                {% endif %}
                                                <button class="btn btn-outline-info" onclick="sendReminder({{ appointment.id }})" title="تذكير">
                                                    <i class="fas fa-bell"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" onclick="viewDetails({{ appointment.id }})" title="التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times text-muted mb-3" style="font-size: 4rem;"></i>
                            <h4 class="text-muted">لا توجد مواعيد</h4>
                            <p class="text-muted">لم يتم حجز أي مواعيد بعد</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div class="modal fade" id="appointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="appointmentDetails">
                <!-- Details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterAppointments() {
    const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('#appointmentsTable tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const status = row.dataset.status.toLowerCase();
        const date = row.dataset.date;
        const searchText = row.dataset.search;
        
        let showRow = true;
        
        // Status filter
        if (statusFilter && !status.includes(statusFilter)) {
            showRow = false;
        }
        
        // Date range filter
        if (dateFrom && date < dateFrom) {
            showRow = false;
        }
        if (dateTo && date > dateTo) {
            showRow = false;
        }
        
        // Search filter
        if (searchInput && !searchText.includes(searchInput)) {
            showRow = false;
        }
        
        if (showRow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    document.getElementById('appointmentCount').textContent = `${visibleCount} موعد`;
}

function markCompleted(appointmentId) {
    if (confirm('هل تريد تأكيد إتمام هذا الموعد؟')) {
        // Here you would make an AJAX call to update the appointment status
        alert('ميزة تحديث الحالة قيد التطوير');
    }
}

function cancelAppointment(appointmentId) {
    if (confirm('هل تريد إلغاء هذا الموعد؟')) {
        window.location.href = `/cancel/${appointmentId}`;
    }
}

function sendReminder(appointmentId) {
    if (confirm('هل تريد إرسال تذكير للعميل؟')) {
        alert('ميزة إرسال التذكير قيد التطوير');
    }
}

function viewDetails(appointmentId) {
    // Here you would load appointment details via AJAX
    document.getElementById('appointmentDetails').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin"></i>
            جاري تحميل التفاصيل...
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
    modal.show();
    
    // Simulate loading details
    setTimeout(() => {
        document.getElementById('appointmentDetails').innerHTML = `
            <p>تفاصيل الموعد رقم ${appointmentId} ستظهر هنا.</p>
            <p>هذه الميزة قيد التطوير.</p>
        `;
    }, 1000);
}
</script>
{% endblock %}
