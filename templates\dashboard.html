{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام حجز المواعيد{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="fw-bold">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                        مرحباً، {{ current_user.username }}
                    </h1>
                    <p class="text-muted">إدارة مواعيدك وحجوزاتك</p>
                </div>
                <a href="{{ url_for('book_appointment') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>حجز موعد جديد
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-5">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">إجمالي المواعيد</h5>
                            <h2 class="fw-bold">{{ appointments|length }}</h2>
                        </div>
                        <i class="fas fa-calendar-alt" style="font-size: 3rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">المواعيد المؤكدة</h5>
                            <h2 class="fw-bold">{{ appointments|selectattr('status', 'equalto', 'مؤكد')|list|length }}</h2>
                        </div>
                        <i class="fas fa-check-circle" style="font-size: 3rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">المواعيد القادمة</h5>
                            <h2 class="fw-bold">
                                {% set upcoming_count = 0 %}
                                {% for appointment in appointments %}
                                    {% if appointment.status == 'مؤكد' and appointment.appointment_date > now %}
                                        {% set upcoming_count = upcoming_count + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {{ upcoming_count }}
                            </h2>
                        </div>
                        <i class="fas fa-clock" style="font-size: 3rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>مواعيدي
                    </h5>
                </div>
                <div class="card-body">
                    {% if appointments %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الخدمة</th>
                                        <th>التاريخ والوقت</th>
                                        <th>المدة</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>الملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in appointments %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-{{ 'cut' if 'قص' in appointment.service.name else 'stethoscope' if 'طبي' in appointment.service.name else 'user-md' }} text-primary me-2"></i>
                                                <strong>{{ appointment.service.name }}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ appointment.appointment_date.strftime('%Y-%m-%d') }}
                                                <br>
                                                <i class="fas fa-clock me-1"></i>
                                                {{ appointment.appointment_date.strftime('%H:%M') }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ appointment.service.duration }} دقيقة
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                {{ appointment.service.price }} ريال
                                            </span>
                                        </td>
                                        <td>
                                            {% if appointment.status == 'مؤكد' %}
                                                <span class="status-badge status-confirmed">
                                                    <i class="fas fa-check me-1"></i>مؤكد
                                                </span>
                                            {% elif appointment.status == 'ملغي' %}
                                                <span class="status-badge status-cancelled">
                                                    <i class="fas fa-times me-1"></i>ملغي
                                                </span>
                                            {% elif appointment.status == 'مكتمل' %}
                                                <span class="status-badge status-completed">
                                                    <i class="fas fa-check-double me-1"></i>مكتمل
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if appointment.notes %}
                                                <span class="text-muted" title="{{ appointment.notes }}">
                                                    {{ appointment.notes[:30] }}{% if appointment.notes|length > 30 %}...{% endif %}
                                                </span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if appointment.status == 'مؤكد' and appointment.appointment_date > now %}
                                                <a href="{{ url_for('cancel_appointment', appointment_id=appointment.id) }}" 
                                                   class="btn btn-sm btn-outline-danger"
                                                   onclick="return confirm('هل أنت متأكد من إلغاء هذا الموعد؟')">
                                                    <i class="fas fa-times me-1"></i>إلغاء
                                                </a>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times text-muted mb-3" style="font-size: 4rem;"></i>
                            <h4 class="text-muted">لا توجد مواعيد</h4>
                            <p class="text-muted">لم تقم بحجز أي مواعيد بعد</p>
                            <a href="{{ url_for('book_appointment') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>احجز موعدك الأول
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Add moment.js for date comparisons
document.addEventListener('DOMContentLoaded', function() {
    // Highlight upcoming appointments
    const rows = document.querySelectorAll('tbody tr');
    const now = new Date();
    
    rows.forEach(row => {
        const dateCell = row.cells[1];
        const statusCell = row.cells[4];
        
        if (dateCell && statusCell.textContent.includes('مؤكد')) {
            const dateText = dateCell.textContent.trim();
            const appointmentDate = new Date(dateText.split('\n')[0].trim());
            
            if (appointmentDate > now) {
                row.classList.add('table-warning');
            }
        }
    });
});
</script>
{% endblock %}
