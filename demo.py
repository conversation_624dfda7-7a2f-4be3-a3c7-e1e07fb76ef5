#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لنظام حجز المواعيد
Demo script for Appointment Booking System
"""

from datetime import datetime, timedelta
from app import app, db, User, Service, Appointment
import random

def create_demo_users():
    """إنشاء مستخدمين تجريبيين"""
    print("إنشاء مستخدمين تجريبيين...")
    
    demo_users = [
        {
            'username': 'ahmed_ali',
            'email': '<EMAIL>',
            'phone': '+966501111111',
            'password': 'demo123'
        },
        {
            'username': 'sara_mohammed',
            'email': '<EMAIL>',
            'phone': '+966502222222',
            'password': 'demo123'
        },
        {
            'username': 'omar_hassan',
            'email': '<EMAIL>',
            'phone': '+966503333333',
            'password': 'demo123'
        },
        {
            'username': 'fatima_ahmad',
            'email': '<EMAIL>',
            'phone': '+966504444444',
            'password': 'demo123'
        },
        {
            'username': 'khalid_salem',
            'email': '<EMAIL>',
            'phone': '+966505555555',
            'password': 'demo123'
        }
    ]
    
    created_users = []
    for user_data in demo_users:
        # التحقق من عدم وجود المستخدم
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if existing_user:
            print(f"المستخدم {user_data['username']} موجود بالفعل")
            created_users.append(existing_user)
            continue
        
        user = User(
            username=user_data['username'],
            email=user_data['email'],
            phone=user_data['phone']
        )
        user.set_password(user_data['password'])
        db.session.add(user)
        created_users.append(user)
    
    db.session.commit()
    print(f"تم إنشاء {len(created_users)} مستخدم تجريبي")
    return created_users

def create_demo_appointments():
    """إنشاء مواعيد تجريبية"""
    print("إنشاء مواعيد تجريبية...")
    
    users = User.query.filter_by(is_admin=False).all()
    services = Service.query.filter_by(is_active=True).all()
    
    if not users or not services:
        print("لا توجد مستخدمين أو خدمات لإنشاء مواعيد تجريبية")
        return
    
    # إنشاء مواعيد للأسبوع القادم
    appointments_created = 0
    
    for day_offset in range(1, 8):  # الأسبوع القادم
        appointment_date = datetime.now() + timedelta(days=day_offset)
        
        # إنشاء 2-5 مواعيد لكل يوم
        num_appointments = random.randint(2, 5)
        
        for _ in range(num_appointments):
            # اختيار وقت عشوائي بين 9 صباحاً و 5 مساءً
            hour = random.randint(9, 17)
            minute = random.choice([0, 30])
            
            appointment_datetime = appointment_date.replace(
                hour=hour, 
                minute=minute, 
                second=0, 
                microsecond=0
            )
            
            # التحقق من عدم تضارب المواعيد
            existing = Appointment.query.filter_by(
                appointment_date=appointment_datetime
            ).first()
            
            if existing:
                continue
            
            # اختيار مستخدم وخدمة عشوائيين
            user = random.choice(users)
            service = random.choice(services)
            
            appointment = Appointment(
                user_id=user.id,
                service_id=service.id,
                appointment_date=appointment_datetime,
                status='مؤكد',
                notes=f'موعد تجريبي لـ {user.username}'
            )
            
            db.session.add(appointment)
            appointments_created += 1
    
    db.session.commit()
    print(f"تم إنشاء {appointments_created} موعد تجريبي")

def show_demo_stats():
    """عرض إحصائيات النظام التجريبي"""
    print("\n" + "=" * 50)
    print("📊 إحصائيات النظام التجريبي")
    print("=" * 50)
    
    # إحصائيات عامة
    total_users = User.query.count()
    admin_users = User.query.filter_by(is_admin=True).count()
    regular_users = total_users - admin_users
    total_services = Service.query.count()
    active_services = Service.query.filter_by(is_active=True).count()
    total_appointments = Appointment.query.count()
    confirmed_appointments = Appointment.query.filter_by(status='مؤكد').count()
    
    print(f"👥 إجمالي المستخدمين: {total_users}")
    print(f"   - مستخدمين عاديين: {regular_users}")
    print(f"   - مديرين: {admin_users}")
    
    print(f"\n🛠️  إجمالي الخدمات: {total_services}")
    print(f"   - خدمات نشطة: {active_services}")
    
    print(f"\n📅 إجمالي المواعيد: {total_appointments}")
    print(f"   - مواعيد مؤكدة: {confirmed_appointments}")
    
    # مواعيد اليوم
    today = datetime.now().date()
    today_appointments = Appointment.query.filter(
        db.func.date(Appointment.appointment_date) == today,
        Appointment.status == 'مؤكد'
    ).count()
    
    print(f"   - مواعيد اليوم: {today_appointments}")
    
    # الإيرادات المتوقعة
    total_revenue = db.session.query(
        db.func.sum(Service.price)
    ).join(Appointment).filter(
        Appointment.status == 'مؤكد'
    ).scalar() or 0
    
    print(f"\n💰 الإيرادات المتوقعة: {total_revenue:.2f} ريال")

def show_upcoming_appointments():
    """عرض المواعيد القادمة"""
    print("\n" + "=" * 50)
    print("📅 المواعيد القادمة (الأسبوع القادم)")
    print("=" * 50)
    
    upcoming = Appointment.query.filter(
        Appointment.appointment_date > datetime.now(),
        Appointment.status == 'مؤكد'
    ).order_by(Appointment.appointment_date).limit(10).all()
    
    if not upcoming:
        print("لا توجد مواعيد قادمة")
        return
    
    for appointment in upcoming:
        print(f"📍 {appointment.appointment_date.strftime('%Y-%m-%d %H:%M')}")
        print(f"   👤 العميل: {appointment.user.username}")
        print(f"   🛠️  الخدمة: {appointment.service.name}")
        print(f"   💰 السعر: {appointment.service.price} ريال")
        print(f"   ⏱️  المدة: {appointment.service.duration} دقيقة")
        print()

def main():
    """الدالة الرئيسية للعرض التوضيحي"""
    print("=" * 60)
    print("🎬 العرض التوضيحي لنظام حجز المواعيد")
    print("📅 Appointment Booking System Demo")
    print("=" * 60)
    
    with app.app_context():
        # التأكد من وجود قاعدة البيانات
        db.create_all()
        
        # إنشاء بيانات تجريبية
        create_demo_users()
        create_demo_appointments()
        
        # عرض الإحصائيات
        show_demo_stats()
        show_upcoming_appointments()
        
        print("\n" + "=" * 60)
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")
        print("=" * 60)
        
        print("\n📋 بيانات تسجيل الدخول التجريبية:")
        print("👨‍💼 المدير:")
        print("   - اسم المستخدم: admin")
        print("   - كلمة المرور: admin123")
        
        print("\n👤 المستخدمين التجريبيين:")
        demo_usernames = ['ahmed_ali', 'sara_mohammed', 'omar_hassan', 'fatima_ahmad', 'khalid_salem']
        for username in demo_usernames:
            print(f"   - اسم المستخدم: {username}")
            print(f"   - كلمة المرور: demo123")
        
        print("\n🚀 لتشغيل النظام:")
        print("   python app.py")
        print("   ثم افتح: http://localhost:5000")
        
        print("\n🎯 جرب الميزات التالية:")
        print("   ✓ تسجيل الدخول بحسابات مختلفة")
        print("   ✓ حجز مواعيد جديدة")
        print("   ✓ عرض لوحة تحكم المستخدم")
        print("   ✓ استخدام لوحة الإدارة")
        print("   ✓ إلغاء وتعديل المواعيد")

if __name__ == '__main__':
    main()
