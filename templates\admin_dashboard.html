{% extends "base.html" %}

{% block title %}لوحة الإدارة - نظام حجز المواعيد{% endblock %}

{% block content %}
<div class="container my-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="fw-bold">
                <i class="fas fa-cog text-primary me-2"></i>
                لوحة الإدارة
            </h1>
            <p class="text-muted">إدارة شاملة لنظام حجز المواعيد</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-5">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">مواعيد اليوم</h6>
                            <h2 class="fw-bold">{{ appointments_today|length }}</h2>
                        </div>
                        <i class="fas fa-calendar-day" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي المواعيد</h6>
                            <h2 class="fw-bold">{{ total_appointments }}</h2>
                        </div>
                        <i class="fas fa-calendar-alt" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">إجمالي المستخدمين</h6>
                            <h2 class="fw-bold">{{ total_users }}</h2>
                        </div>
                        <i class="fas fa-users" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">الإيرادات المتوقعة</h6>
                            <h2 class="fw-bold">
                                {{ appointments_today|sum(attribute='service.price')|int }} ريال
                            </h2>
                        </div>
                        <i class="fas fa-money-bill-wave" style="font-size: 2.5rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ url_for('admin_appointments') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>
                                إدارة المواعيد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100" onclick="exportData()">
                                <i class="fas fa-download me-2"></i>
                                تصدير البيانات
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info w-100" onclick="sendBulkReminders()">
                                <i class="fas fa-bell me-2"></i>
                                إرسال تذكيرات
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-warning w-100" onclick="viewReports()">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Appointments -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>مواعيد اليوم
                    </h5>
                    <span class="badge bg-primary">{{ appointments_today|length }} موعد</span>
                </div>
                <div class="card-body">
                    {% if appointments_today %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الوقت</th>
                                        <th>العميل</th>
                                        <th>الخدمة</th>
                                        <th>المدة</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in appointments_today|sort(attribute='appointment_date') %}
                                    <tr>
                                        <td>
                                            <strong>{{ appointment.appointment_date.strftime('%H:%M') }}</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ appointment.user.username }}</strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    {{ appointment.user.email }}
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-{{ 'cut' if 'قص' in appointment.service.name else 'stethoscope' if 'طبي' in appointment.service.name else 'user-md' }} text-primary me-2"></i>
                                                {{ appointment.service.name }}
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                {{ appointment.service.duration }} دقيقة
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold text-success">
                                                {{ appointment.service.price }} ريال
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-confirmed">
                                                <i class="fas fa-check me-1"></i>مؤكد
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-success" onclick="markCompleted({{ appointment.id }})" title="تم">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="cancelAppointment({{ appointment.id }})" title="إلغاء">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="sendReminder({{ appointment.id }})" title="تذكير">
                                                    <i class="fas fa-bell"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times text-muted mb-3" style="font-size: 4rem;"></i>
                            <h4 class="text-muted">لا توجد مواعيد اليوم</h4>
                            <p class="text-muted">لا توجد مواعيد مجدولة لهذا اليوم</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function markCompleted(appointmentId) {
    if (confirm('هل تريد تأكيد إتمام هذا الموعد؟')) {
        // Here you would make an AJAX call to update the appointment status
        fetch(`/admin/appointment/${appointmentId}/complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ في تحديث الموعد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function cancelAppointment(appointmentId) {
    if (confirm('هل تريد إلغاء هذا الموعد؟')) {
        window.location.href = `/cancel/${appointmentId}`;
    }
}

function sendReminder(appointmentId) {
    if (confirm('هل تريد إرسال تذكير للعميل؟')) {
        fetch(`/admin/appointment/${appointmentId}/remind`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال التذكير بنجاح');
            } else {
                alert('حدث خطأ في إرسال التذكير');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
}

function exportData() {
    alert('ميزة تصدير البيانات قيد التطوير');
}

function sendBulkReminders() {
    if (confirm('هل تريد إرسال تذكيرات لجميع مواعيد الغد؟')) {
        alert('ميزة الإرسال الجماعي قيد التطوير');
    }
}

function viewReports() {
    alert('ميزة التقارير قيد التطوير');
}
</script>
{% endblock %}
