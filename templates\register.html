{% extends "base.html" %}

{% block title %}إنشاء حساب - نظام حجز المواعيد{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus text-primary mb-3" style="font-size: 3rem;"></i>
                        <h2 class="fw-bold">إنشاء حساب جديد</h2>
                        <p class="text-muted">انضم إلينا واحجز موعدك بسهولة</p>
                    </div>
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-2"></i>اسم المستخدم
                            </label>
                            {{ form.username(class="form-control", id="username", placeholder="أدخل اسم المستخدم") }}
                            {% if form.username.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                            </label>
                            {{ form.email(class="form-control", id="email", placeholder="أدخل بريدك الإلكتروني") }}
                            {% if form.email.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-2"></i>رقم الهاتف (اختياري)
                            </label>
                            {{ form.phone(class="form-control", id="phone", placeholder="أدخل رقم هاتفك") }}
                            {% if form.phone.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.phone.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>كلمة المرور
                            </label>
                            {{ form.password(class="form-control", type="password", id="password", placeholder="أدخل كلمة مرور قوية") }}
                            {% if form.password.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="text-muted">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                        </div>
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <p class="mb-0">
                                لديك حساب بالفعل؟ 
                                <a href="{{ url_for('login') }}" class="text-decoration-none">
                                    <i class="fas fa-sign-in-alt me-1"></i>سجل دخولك هنا
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Features -->
            <div class="row mt-4 text-center">
                <div class="col-4">
                    <i class="fas fa-shield-alt text-success mb-2" style="font-size: 2rem;"></i>
                    <p class="small text-muted">آمن ومحمي</p>
                </div>
                <div class="col-4">
                    <i class="fas fa-bell text-primary mb-2" style="font-size: 2rem;"></i>
                    <p class="small text-muted">إشعارات تلقائية</p>
                </div>
                <div class="col-4">
                    <i class="fas fa-clock text-info mb-2" style="font-size: 2rem;"></i>
                    <p class="small text-muted">حجز سريع</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
