#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام حجز المواعيد
Simple launcher for the appointment booking system
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Flask components
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import StringField, DateTimeField, SelectField, TextAreaField, EmailField, TelField, PasswordField
from wtforms.validators import DataRequired, Email, Length
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here-change-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///appointments.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database and login manager
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    password_hash = db.Column(db.String(120), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    appointments = db.relationship('Appointment', backref='user', lazy=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Service(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    duration = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    
    appointments = db.relationship('Appointment', backref='service', lazy=True)

class Appointment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('service.id'), nullable=False)
    appointment_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='مؤكد')
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    reminder_sent = db.Column(db.Boolean, default=False)

# Forms
class AppointmentForm(FlaskForm):
    service_id = SelectField('الخدمة', coerce=int, validators=[DataRequired()])
    appointment_date = DateTimeField('تاريخ ووقت الموعد', validators=[DataRequired()])
    notes = TextAreaField('ملاحظات إضافية')

class UserRegistrationForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)])
    email = EmailField('البريد الإلكتروني', validators=[DataRequired(), Email()])
    phone = TelField('رقم الهاتف')
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)])

class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])

# Simple notification function
def send_notification(appointment, notification_type="confirmation"):
    print(f"📧 إشعار {notification_type} للعميل: {appointment.user.username}")
    print(f"📧 الخدمة: {appointment.service.name}")
    print(f"📧 الموعد: {appointment.appointment_date}")
    return True

# Routes
@app.route('/')
def index():
    services = Service.query.filter_by(is_active=True).all()
    return render_template('index.html', services=services)

@app.route('/register', methods=['GET', 'POST'])
def register():
    form = UserRegistrationForm()
    if form.validate_on_submit():
        if User.query.filter_by(username=form.username.data).first():
            flash('اسم المستخدم موجود مسبقاً', 'error')
            return render_template('register.html', form=form)
        
        if User.query.filter_by(email=form.email.data).first():
            flash('البريد الإلكتروني مسجل مسبقاً', 'error')
            return render_template('register.html', form=form)
        
        user = User(
            username=form.username.data,
            email=form.email.data,
            phone=form.phone.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        
        flash('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.', 'success')
        return redirect(url_for('login'))
    
    return render_template('register.html', form=form)

@app.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            flash('تم تسجيل الدخول بنجاح!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    appointments = Appointment.query.filter_by(user_id=current_user.id).order_by(Appointment.appointment_date.desc()).all()
    return render_template('dashboard.html', appointments=appointments, now=datetime.now())

@app.route('/book', methods=['GET', 'POST'])
@login_required
def book_appointment():
    form = AppointmentForm()
    services = Service.query.filter_by(is_active=True).all()
    form.service_id.choices = [(s.id, f"{s.name} - {s.price} ريال") for s in services]
    
    if form.validate_on_submit():
        if form.appointment_date.data <= datetime.now():
            flash('يجب أن يكون الموعد في المستقبل', 'error')
            return render_template('book.html', form=form, services=services, today=datetime.now().strftime('%Y-%m-%d'))
        
        appointment = Appointment(
            user_id=current_user.id,
            service_id=form.service_id.data,
            appointment_date=form.appointment_date.data,
            notes=form.notes.data
        )
        db.session.add(appointment)
        db.session.commit()
        
        send_notification(appointment, "confirmation")
        
        flash('تم حجز الموعد بنجاح!', 'success')
        return redirect(url_for('dashboard'))
    
    return render_template('book.html', form=form, services=services, today=datetime.now().strftime('%Y-%m-%d'))

@app.route('/cancel/<int:appointment_id>')
@login_required
def cancel_appointment(appointment_id):
    appointment = Appointment.query.get_or_404(appointment_id)
    
    if appointment.user_id != current_user.id and not current_user.is_admin:
        flash('غير مسموح لك بإلغاء هذا الموعد', 'error')
        return redirect(url_for('dashboard'))
    
    appointment.status = 'ملغي'
    db.session.commit()
    
    flash('تم إلغاء الموعد بنجاح', 'info')
    return redirect(url_for('dashboard'))

@app.route('/admin')
@login_required
def admin_dashboard():
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    today = datetime.now().date()
    appointments_today = Appointment.query.filter(
        db.func.date(Appointment.appointment_date) == today,
        Appointment.status == 'مؤكد'
    ).all()
    
    total_appointments = Appointment.query.count()
    total_users = User.query.count()
    
    return render_template('admin_dashboard.html', 
                         appointments_today=appointments_today,
                         total_appointments=total_appointments,
                         total_users=total_users)

@app.route('/admin/appointments')
@login_required
def admin_appointments():
    if not current_user.is_admin:
        flash('غير مسموح لك بالوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))
    
    appointments = Appointment.query.order_by(Appointment.appointment_date.desc()).all()
    return render_template('admin_appointments.html', appointments=appointments)

@app.route('/api/available_slots')
def available_slots():
    date_str = request.args.get('date')
    service_id = request.args.get('service_id')
    
    if not date_str or not service_id:
        return jsonify({'error': 'Missing parameters'}), 400
    
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
        service = Service.query.get(service_id)
        
        if not service:
            return jsonify({'error': 'Service not found'}), 404
        
        available_slots = []
        start_time = datetime.combine(date, datetime.min.time().replace(hour=9))
        end_time = datetime.combine(date, datetime.min.time().replace(hour=18))
        
        current_time = start_time
        while current_time < end_time:
            if current_time > datetime.now():
                available_slots.append(current_time.strftime('%H:%M'))
            current_time += timedelta(minutes=30)
        
        return jsonify({'slots': available_slots})
    
    except ValueError:
        return jsonify({'error': 'Invalid date format'}), 400

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("🚀 نظام حجز المواعيد يعمل الآن!")
        print("📅 افتح المتصفح على: http://localhost:3000")
        print("👤 المدير: admin / admin123")

    try:
        app.run(debug=False, host='127.0.0.1', port=3000)
    except OSError as e:
        print(f"خطأ في تشغيل الخادم: {e}")
        print("جرب تشغيل الأمر كمدير أو استخدم منفذ آخر")
