# 📅 نظام حجز المواعيد - Appointment Booking System

نظام متطور لحجز المواعيد مع إشعارات تلقائية، مناسب للأطباء، الحلاقين، وجميع مقدمي الخدمات.

## ✨ المميزات

### 🎯 للعملاء
- **حجز سهل وسريع**: واجهة بسيطة لحجز المواعيد
- **إشعارات تلقائية**: تذكيرات بالبريد الإلكتروني قبل الموعد
- **لوحة تحكم شخصية**: عرض وإدارة جميع المواعيد
- **إلغاء مرن**: إمكانية إلغاء المواعيد بسهولة

### 👨‍💼 للإدارة
- **لوحة تحكم شاملة**: إحصائيات ومتابعة شاملة
- **إدارة المواعيد**: عرض وتعديل جميع المواعيد
- **إدارة الخدمات**: إضافة وتعديل الخدمات المتاحة
- **تقارير مفصلة**: تقارير الإيرادات والإحصائيات

### 🔧 تقنية
- **واجهة متجاوبة**: تعمل على جميع الأجهزة
- **أمان عالي**: حماية البيانات وتشفير كلمات المرور
- **قاعدة بيانات محلية**: SQLite للبساطة والسرعة
- **إشعارات بريدية**: نظام إشعارات تلقائي

## 🚀 التثبيت والإعداد

### المتطلبات
- Python 3.7 أو أحدث
- pip (مدير حزم Python)

### خطوات التثبيت

1. **تحميل المشروع**
```bash
git clone <repository-url>
cd appointment-booking-system
```

2. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

3. **إعداد النظام**
```bash
python setup.py
```

4. **تشغيل النظام**
```bash
python app.py
```

5. **فتح المتصفح**
```
http://localhost:5000
```

## 🔧 الإعداد المتقدم

### إعدادات البريد الإلكتروني

قم بتحديث الإعدادات التالية في `app.py`:

```python
app.config['MAIL_USERNAME'] = '<EMAIL>'
app.config['MAIL_PASSWORD'] = 'your-app-password'
```

**ملاحظة**: لـ Gmail، استخدم App Password بدلاً من كلمة المرور العادية.

### إعدادات قاعدة البيانات

النظام يستخدم SQLite افتراضياً. لاستخدام قاعدة بيانات أخرى:

```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'postgresql://user:pass@localhost/dbname'
```

## 👤 المستخدم الإداري الافتراضي

بعد تشغيل `setup.py`:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: قم بتغيير كلمة المرور فور تسجيل الدخول!

## 📱 كيفية الاستخدام

### للعملاء

1. **إنشاء حساب**
   - انقر على "إنشاء حساب"
   - أدخل بياناتك الأساسية
   - تأكد من صحة البريد الإلكتروني

2. **حجز موعد**
   - اختر الخدمة المطلوبة
   - حدد التاريخ والوقت
   - أضف أي ملاحظات
   - تأكيد الحجز

3. **إدارة المواعيد**
   - عرض جميع مواعيدك من لوحة التحكم
   - إلغاء المواعيد عند الحاجة
   - تلقي تذكيرات تلقائية

### للإدارة

1. **تسجيل الدخول كإداري**
   - استخدم حساب admin
   - الوصول للوحة الإدارة

2. **إدارة المواعيد**
   - عرض مواعيد اليوم
   - تأكيد إتمام المواعيد
   - إرسال تذكيرات يدوية

3. **إدارة الخدمات**
   - إضافة خدمات جديدة
   - تعديل الأسعار والأوقات
   - تفعيل/إلغاء تفعيل الخدمات

## 🎨 تخصيص النظام

### إضافة خدمات جديدة

```python
service = Service(
    name='اسم الخدمة',
    description='وصف الخدمة',
    duration=30,  # بالدقائق
    price=50.0,   # بالريال
    is_active=True
)
db.session.add(service)
db.session.commit()
```

### تخصيص الألوان والتصميم

قم بتعديل ملف `templates/base.html` في قسم `<style>`:

```css
.btn-primary {
    background: linear-gradient(45deg, #your-color, #your-color-dark);
}
```

## 📊 قاعدة البيانات

### الجداول الرئيسية

- **User**: بيانات المستخدمين
- **Service**: الخدمات المتاحة
- **Appointment**: المواعيد المحجوزة
- **Notification**: سجل الإشعارات

### النسخ الاحتياطي

```bash
# نسخ احتياطي لقاعدة البيانات
cp appointments.db appointments_backup_$(date +%Y%m%d).db
```

## 🔒 الأمان

- تشفير كلمات المرور باستخدام Werkzeug
- حماية من CSRF باستخدام Flask-WTF
- جلسات آمنة مع Flask-Login
- تنظيف البيانات المدخلة

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في إرسال البريد**
   - تأكد من إعدادات SMTP
   - استخدم App Password لـ Gmail

2. **خطأ في قاعدة البيانات**
   - احذف `appointments.db` وشغل `setup.py` مرة أخرى

3. **مشاكل في التثبيت**
   - تأكد من إصدار Python (3.7+)
   - استخدم بيئة افتراضية

## 📞 الدعم والمساعدة

للحصول على المساعدة:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع الوثائق التقنية

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push للـ branch
5. فتح Pull Request

## 🎉 شكر خاص

شكراً لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا النظام.

---

**تم تطويره بـ ❤️ لخدمة المجتمع العربي**
