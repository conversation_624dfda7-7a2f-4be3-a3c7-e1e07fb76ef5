#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد نظام حجز المواعيد
Setup script for Appointment Booking System
"""

import os
import sys
from app import app, db, User, Service

def create_admin_user():
    """إنشاء مستخدم إداري"""
    print("إنشاء مستخدم إداري...")
    
    # التحقق من وجود مستخدم إداري
    admin = User.query.filter_by(is_admin=True).first()
    if admin:
        print(f"يوجد مستخدم إداري بالفعل: {admin.username}")
        return
    
    # إنشاء مستخدم إداري جديد
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        phone='+966501234567',
        is_admin=True
    )
    admin_user.set_password('admin123')  # غيّر كلمة المرور في الإنتاج
    
    db.session.add(admin_user)
    db.session.commit()
    
    print("تم إنشاء المستخدم الإداري:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("⚠️  يرجى تغيير كلمة المرور بعد تسجيل الدخول!")

def create_sample_services():
    """إنشاء خدمات تجريبية"""
    print("إنشاء خدمات تجريبية...")
    
    if Service.query.count() > 0:
        print("توجد خدمات بالفعل في النظام")
        return
    
    sample_services = [
        # خدمات الحلاقة
        Service(
            name='قص شعر رجالي عادي',
            description='قص شعر عادي للرجال مع التصفيف',
            duration=30,
            price=25.0,
            is_active=True
        ),
        Service(
            name='حلاقة ذقن وشارب',
            description='حلاقة وتهذيب الذقن والشارب',
            duration=20,
            price=15.0,
            is_active=True
        ),
        Service(
            name='قص وتصفيف متقدم',
            description='قص شعر متقدم مع تصفيف احترافي',
            duration=45,
            price=40.0,
            is_active=True
        ),
        
        # خدمات طبية
        Service(
            name='كشف طبي عام',
            description='فحص طبي شامل وتشخيص أولي',
            duration=45,
            price=100.0,
            is_active=True
        ),
        Service(
            name='استشارة طبية متخصصة',
            description='استشارة طبية مع طبيب متخصص',
            duration=30,
            price=150.0,
            is_active=True
        ),
        Service(
            name='فحص دوري',
            description='فحص دوري وقائي',
            duration=30,
            price=80.0,
            is_active=True
        ),
        
        # خدمات أخرى
        Service(
            name='استشارة قانونية',
            description='استشارة قانونية مع محامي متخصص',
            duration=60,
            price=200.0,
            is_active=True
        ),
        Service(
            name='جلسة علاج طبيعي',
            description='جلسة علاج طبيعي وتأهيل',
            duration=45,
            price=120.0,
            is_active=True
        )
    ]
    
    for service in sample_services:
        db.session.add(service)
    
    db.session.commit()
    print(f"تم إنشاء {len(sample_services)} خدمة تجريبية")

def setup_database():
    """إعداد قاعدة البيانات"""
    print("إعداد قاعدة البيانات...")
    
    # إنشاء الجداول
    db.create_all()
    print("تم إنشاء جداول قاعدة البيانات")
    
    # إنشاء البيانات التجريبية
    create_admin_user()
    create_sample_services()

def check_requirements():
    """التحقق من المتطلبات"""
    print("التحقق من المتطلبات...")
    
    required_packages = [
        'flask',
        'flask-sqlalchemy',
        'flask-wtf',
        'flask-login',
        'wtforms',
        'werkzeug',
        'email-validator'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ المتطلبات التالية مفقودة:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nلتثبيت المتطلبات، استخدم الأمر:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 60)
    print("🏥 مرحباً بك في نظام حجز المواعيد")
    print("📅 Appointment Booking System Setup")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # إعداد قاعدة البيانات
    with app.app_context():
        setup_database()
    
    print("\n" + "=" * 60)
    print("✅ تم إعداد النظام بنجاح!")
    print("=" * 60)
    
    print("\n📋 معلومات مهمة:")
    print("1. المستخدم الإداري:")
    print("   - اسم المستخدم: admin")
    print("   - كلمة المرور: admin123")
    print("   - ⚠️  يرجى تغيير كلمة المرور!")
    
    print("\n2. إعدادات البريد الإلكتروني:")
    print("   - قم بتحديث إعدادات البريد في app.py")
    print("   - MAIL_USERNAME و MAIL_PASSWORD")
    
    print("\n3. لتشغيل النظام:")
    print("   python app.py")
    print("   ثم افتح المتصفح على: http://localhost:5000")
    
    print("\n4. الميزات المتاحة:")
    print("   ✓ حجز المواعيد")
    print("   ✓ إشعارات تلقائية")
    print("   ✓ لوحة تحكم المستخدم")
    print("   ✓ لوحة تحكم الإدارة")
    print("   ✓ إدارة الخدمات")
    
    print("\n🎉 استمتع باستخدام النظام!")

if __name__ == '__main__':
    main()
