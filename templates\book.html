{% extends "base.html" %}

{% block title %}حجز موعد - نظام حجز المواعيد{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>
                        حجز موعد جديد
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" id="bookingForm">
                        {{ form.hidden_tag() }}
                        
                        <!-- Service Selection -->
                        <div class="mb-4">
                            <label for="service_id" class="form-label fw-bold">
                                <i class="fas fa-concierge-bell me-2 text-primary"></i>
                                اختر الخدمة
                            </label>
                            {{ form.service_id(class="form-select", id="service_id", onchange="updateServiceInfo()") }}
                            {% if form.service_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.service_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            
                            <!-- Service Info Display -->
                            <div id="serviceInfo" class="mt-3 p-3 bg-light rounded" style="display: none;">
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">المدة:</small>
                                        <div class="fw-bold" id="serviceDuration">-</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">السعر:</small>
                                        <div class="fw-bold text-success" id="servicePrice">-</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">الوصف:</small>
                                    <div id="serviceDescription">-</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Date Selection -->
                        <div class="mb-4">
                            <label for="appointment_date" class="form-label fw-bold">
                                <i class="fas fa-calendar me-2 text-primary"></i>
                                تاريخ الموعد
                            </label>
                            <input type="date" class="form-control" id="dateInput" onchange="loadAvailableSlots()" min="{{ today }}">
                        </div>
                        
                        <!-- Time Selection -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">
                                <i class="fas fa-clock me-2 text-primary"></i>
                                وقت الموعد
                            </label>
                            <div id="timeSlots" class="row g-2">
                                <div class="col-12 text-center text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    يرجى اختيار التاريخ أولاً لعرض الأوقات المتاحة
                                </div>
                            </div>
                            {{ form.appointment_date(type="hidden", id="appointment_date") }}
                            {% if form.appointment_date.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.appointment_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="notes" class="form-label fw-bold">
                                <i class="fas fa-sticky-note me-2 text-primary"></i>
                                ملاحظات إضافية (اختياري)
                            </label>
                            {{ form.notes(class="form-control", id="notes", rows="3", placeholder="أي ملاحظات أو طلبات خاصة...") }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitBtn" disabled>
                                <i class="fas fa-check me-2"></i>تأكيد الحجز
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Booking Info -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="fw-bold mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        معلومات مهمة
                    </h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-bell text-warning me-2"></i>
                            ستصلك رسالة تأكيد على بريدك الإلكتروني
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-info me-2"></i>
                            سيتم إرسال تذكير قبل موعدك بيوم واحد
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times-circle text-danger me-2"></i>
                            يمكنك إلغاء الموعد من لوحة التحكم
                        </li>
                        <li>
                            <i class="fas fa-phone text-success me-2"></i>
                            للاستفسارات: +966 50 123 4567
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Service information data
const services = {
    {% for choice in form.service_id.choices %}
    {{ choice[0] }}: {
        name: "{{ choice[1].split(' - ')[0] }}",
        price: "{{ choice[1].split(' - ')[1] }}",
        duration: "{{ services[loop.index0].duration if services else '30' }} دقيقة",
        description: "{{ services[loop.index0].description if services else 'وصف الخدمة' }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
};

function updateServiceInfo() {
    const serviceSelect = document.getElementById('service_id');
    const serviceInfo = document.getElementById('serviceInfo');
    const serviceId = serviceSelect.value;
    
    if (serviceId && services[serviceId]) {
        const service = services[serviceId];
        document.getElementById('serviceDuration').textContent = service.duration;
        document.getElementById('servicePrice').textContent = service.price;
        document.getElementById('serviceDescription').textContent = service.description;
        serviceInfo.style.display = 'block';
    } else {
        serviceInfo.style.display = 'none';
    }
    
    // Reload available slots when service changes
    loadAvailableSlots();
}

function loadAvailableSlots() {
    const dateInput = document.getElementById('dateInput');
    const serviceSelect = document.getElementById('service_id');
    const timeSlotsContainer = document.getElementById('timeSlots');
    const submitBtn = document.getElementById('submitBtn');
    
    const date = dateInput.value;
    const serviceId = serviceSelect.value;
    
    if (!date || !serviceId) {
        timeSlotsContainer.innerHTML = `
            <div class="col-12 text-center text-muted">
                <i class="fas fa-info-circle me-1"></i>
                يرجى اختيار الخدمة والتاريخ أولاً
            </div>
        `;
        submitBtn.disabled = true;
        return;
    }
    
    // Show loading
    timeSlotsContainer.innerHTML = `
        <div class="col-12 text-center">
            <i class="fas fa-spinner fa-spin me-2"></i>
            جاري تحميل الأوقات المتاحة...
        </div>
    `;
    
    // Fetch available slots
    fetch(`/api/available_slots?date=${date}&service_id=${serviceId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                timeSlotsContainer.innerHTML = `
                    <div class="col-12 text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        ${data.error}
                    </div>
                `;
                return;
            }
            
            if (data.slots.length === 0) {
                timeSlotsContainer.innerHTML = `
                    <div class="col-12 text-center text-warning">
                        <i class="fas fa-calendar-times me-1"></i>
                        لا توجد أوقات متاحة في هذا التاريخ
                    </div>
                `;
                submitBtn.disabled = true;
                return;
            }
            
            // Display available slots
            let slotsHtml = '';
            data.slots.forEach(slot => {
                slotsHtml += `
                    <div class="col-6 col-md-4">
                        <input type="radio" class="btn-check" name="timeSlot" id="slot_${slot}" value="${slot}" onchange="selectTimeSlot('${slot}')">
                        <label class="btn btn-outline-primary w-100" for="slot_${slot}">
                            <i class="fas fa-clock me-1"></i>${slot}
                        </label>
                    </div>
                `;
            });
            
            timeSlotsContainer.innerHTML = slotsHtml;
            submitBtn.disabled = true;
        })
        .catch(error => {
            console.error('Error:', error);
            timeSlotsContainer.innerHTML = `
                <div class="col-12 text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    حدث خطأ في تحميل الأوقات المتاحة
                </div>
            `;
        });
}

function selectTimeSlot(time) {
    const dateInput = document.getElementById('dateInput');
    const appointmentDateInput = document.getElementById('appointment_date');
    const submitBtn = document.getElementById('submitBtn');
    
    const date = dateInput.value;
    const datetime = `${date} ${time}`;
    
    // Set the hidden datetime field
    appointmentDateInput.value = datetime;
    
    // Enable submit button
    submitBtn.disabled = false;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateServiceInfo();
});
</script>
{% endblock %}
