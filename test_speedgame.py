#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the speedgame fixes work properly.
"""

import sys
import os

# إصلاح مشكلة عرض النصوص العربية في وحدة التحكم
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

def test_arabic_display():
    """Test if Arabic text displays correctly."""
    print("🤖 اختبار عرض النص العربي...")
    print("✅ إذا كنت ترى هذا النص بوضوح، فإن الإصلاح يعمل!")
    print("🎮 لعبة الأسرع - Speed Game")
    print("🏆 مبروك! النص العربي يعمل بشكل صحيح")

def test_words_file():
    """Test if words.txt file exists and can be read."""
    try:
        with open("words.txt", "r", encoding="utf-8") as f:
            words = [word.strip() for word in f.readlines() if word.strip()]
        
        if words:
            print(f"✅ تم تحميل {len(words)} كلمة من ملف words.txt")
            print("🔤 أول 5 كلمات:")
            for i, word in enumerate(words[:5]):
                print(f"   {i+1}. {word}")
            return True
        else:
            print("❌ ملف words.txt فارغ")
            return False
            
    except FileNotFoundError:
        print("❌ ملف words.txt غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف words.txt: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 50)
    print("🧪 اختبار إصلاحات لعبة الأسرع")
    print("=" * 50)
    
    # Test 1: Arabic display
    print("\n📝 اختبار 1: عرض النص العربي")
    test_arabic_display()
    
    # Test 2: Words file
    print("\n📁 اختبار 2: ملف الكلمات")
    words_ok = test_words_file()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    print("✅ عرض النص العربي: يعمل")
    print(f"{'✅' if words_ok else '❌'} ملف الكلمات: {'يعمل' if words_ok else 'لا يعمل'}")
    
    if words_ok:
        print("\n🎉 جميع الاختبارات نجحت! يمكنك الآن تشغيل speedgame.py")
        print("💡 لتشغيل البوت: python speedgame.py")
    else:
        print("\n⚠️  هناك مشاكل تحتاج إصلاح قبل تشغيل البوت")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
